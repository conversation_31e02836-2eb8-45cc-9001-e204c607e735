"use client"

import * as React from "react"
import { createPortal } from "react-dom"
import { Check } from "lucide-react"
import { Pop<PERSON>, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { useTags, useCreateTag, useAddTagToTask, useRemoveTagFromTask, useDeleteTag } from "@/hooks/use-tags"
import { FrontendTag } from "@/lib/api/tag-service"

interface TagSelectorPopoverProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedTags: string[]
  onTagsChange: (tags: string[]) => void
  triggerElement?: HTMLElement | null
  taskId?: string // Add taskId for direct tag operations
}

// This component now fetches tags from the database

export function TagSelectorPopover({ open, onOpenChange, selectedTags, onTagsChange, triggerElement, taskId }: TagSelectorPopoverProps) {
  const [searchQuery, setSearchQuery] = React.useState("")
  const [localSelectedTags, setLocalSelectedTags] = React.useState<string[]>(selectedTags)
  const [position, setPosition] = React.useState({ x: 0, y: 0 })
  const [selectedTagsForDeletion, setSelectedTagsForDeletion] = React.useState<string[]>([])
  const [isDeleteMode, setIsDeleteMode] = React.useState(false)
  const containerRef = React.useRef<HTMLDivElement>(null)

  // Database hooks
  const { data: availableTags = [], isLoading: tagsLoading } = useTags()
  const createTagMutation = useCreateTag()
  const addTagToTaskMutation = useAddTagToTask()
  const removeTagFromTaskMutation = useRemoveTagFromTask()
  const deleteTagMutation = useDeleteTag()

  // Reset local state when the popover opens
  React.useEffect(() => {
    if (open) {
      setLocalSelectedTags(selectedTags)
      setSearchQuery("")
      setSelectedTagsForDeletion([])
      setIsDeleteMode(false)

      // Update position when the popover opens
      if (triggerElement) {
        const rect = triggerElement.getBoundingClientRect()
        setPosition({
          x: rect.left,
          y: rect.bottom + window.scrollY
        })
      }
    }
  }, [open, selectedTags, triggerElement])

  // Handle click outside to close the selector
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        onOpenChange(false)
      }
    }

    if (open) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [open, onOpenChange])

  // Handle click outside to close
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node) &&
          triggerElement && !triggerElement.contains(event.target as Node)) {
        onOpenChange(false)
      }
    }

    if (open) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [open, onOpenChange, triggerElement])

  // Handle keyboard events for tag deletion
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!open) return

      if (event.key === 'Delete' || event.key === 'Backspace') {
        if (selectedTagsForDeletion.length > 0) {
          handleDeleteSelectedTags()
        }
      } else if (event.key === 'Escape') {
        setIsDeleteMode(false)
        setSelectedTagsForDeletion([])
      }
    }

    if (open) {
      document.addEventListener("keydown", handleKeyDown)
      return () => {
        document.removeEventListener("keydown", handleKeyDown)
      }
    }
  }, [open, selectedTagsForDeletion, handleDeleteSelectedTags])

  // Filter tags based on search query
  const filteredTags = availableTags.filter((tag) => tag.name.toLowerCase().includes(searchQuery.toLowerCase()))

  // Toggle tag selection
  const toggleTag = async (tagName: string) => {
    const isCurrentlySelected = localSelectedTags.includes(tagName)

    if (taskId) {
      // Direct database operations when taskId is provided
      const tag = availableTags.find(t => t.name === tagName)
      if (tag) {
        if (isCurrentlySelected) {
          await removeTagFromTaskMutation.mutateAsync({ taskId, tagId: tag.id })
        } else {
          await addTagToTaskMutation.mutateAsync({ taskId, tagId: tag.id })
        }
      }
    }

    // Update local state
    setLocalSelectedTags((prev) => (prev.includes(tagName) ? prev.filter((t) => t !== tagName) : [...prev, tagName]))
  }

  // Create new tag from search query
  const handleCreateTag = async () => {
    if (searchQuery.trim() && !availableTags.some(tag => tag.name.toLowerCase() === searchQuery.toLowerCase())) {
      try {
        const newTag = await createTagMutation.mutateAsync({ name: searchQuery.trim() })
        if (newTag && taskId) {
          // Automatically add the new tag to the task
          await addTagToTaskMutation.mutateAsync({ taskId, tagId: newTag.id })
          setLocalSelectedTags(prev => [...prev, newTag.name])
        }
        setSearchQuery("")
      } catch (error) {
        console.error('Failed to create tag:', error)
      }
    }
  }

  // Handle tag selection for deletion
  const toggleTagForDeletion = (tagName: string) => {
    setSelectedTagsForDeletion(prev =>
      prev.includes(tagName)
        ? prev.filter(t => t !== tagName)
        : [...prev, tagName]
    )
  }

  // Delete selected tags
  const handleDeleteSelectedTags = React.useCallback(async () => {
    if (selectedTagsForDeletion.length === 0) return

    try {
      for (const tagName of selectedTagsForDeletion) {
        const tag = availableTags.find(t => t.name === tagName)
        if (tag) {
          await deleteTagMutation.mutateAsync(tag.id)
        }
      }
      setSelectedTagsForDeletion([])
      setIsDeleteMode(false)
    } catch (error) {
      console.error('Failed to delete tags:', error)
    }
  }, [selectedTagsForDeletion, availableTags, deleteTagMutation])

  // Apply changes (for non-direct mode)
  const handleApply = () => {
    onTagsChange(localSelectedTags)
    onOpenChange(false)
  }

  if (!open) return null

  // Use portal to render outside any scrollable containers
  return typeof document !== "undefined" ? createPortal(
    <div
      ref={containerRef}
      className="fixed z-50 bg-white rounded-lg shadow-lg border border-border"
      style={{
        left: position.x,
        top: position.y,
        width: "256px" // w-64 equivalent
      }}
    >
        <div className="p-2 border-b space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Tags</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setIsDeleteMode(!isDeleteMode)
                setSelectedTagsForDeletion([])
              }}
              className={cn(
                "h-6 px-2 text-xs",
                isDeleteMode ? "bg-red-100 text-red-700 hover:bg-red-200" : ""
              )}
            >
              {isDeleteMode ? "Cancel" : "Delete"}
            </Button>
          </div>
          {!isDeleteMode && (
            <Input
              placeholder="Search tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleCreateTag()
                }
              }}
              className="h-8"
            />
          )}
          {isDeleteMode && selectedTagsForDeletion.length > 0 && (
            <div className="text-xs text-red-600">
              {selectedTagsForDeletion.length} tag(s) selected. Press Delete to remove.
            </div>
          )}
        </div>
        <div className="max-h-60 overflow-y-auto py-1">
          {tagsLoading ? (
            <div className="px-3 py-2 text-sm text-gray-500">Loading tags...</div>
          ) : (
            <>
              {/* Show create option if search doesn't match existing tags */}
              {searchQuery.trim() && !availableTags.some(tag => tag.name.toLowerCase() === searchQuery.toLowerCase()) && (
                <div
                  className="flex items-center px-3 py-1.5 cursor-pointer hover:bg-gray-100 border-b"
                  onClick={handleCreateTag}
                >
                  <div className="w-5 h-5 mr-2 flex items-center justify-center">
                    <span className="text-xs">+</span>
                  </div>
                  <span className="text-sm">Create "{searchQuery.trim()}"</span>
                </div>
              )}

              {filteredTags.map((tag) => (
                <div
                  key={tag.id}
                  className={cn(
                    "flex items-center px-3 py-1.5 cursor-pointer hover:bg-gray-100",
                    !isDeleteMode && localSelectedTags.includes(tag.name) && "bg-gray-50",
                    isDeleteMode && selectedTagsForDeletion.includes(tag.name) && "bg-red-100 border-red-200",
                  )}
                  onClick={() => isDeleteMode ? toggleTagForDeletion(tag.name) : toggleTag(tag.name)}
                >
                  <div className="w-5 h-5 mr-2 flex items-center justify-center">
                    {!isDeleteMode && localSelectedTags.includes(tag.name) && <Check className="h-4 w-4" />}
                    {isDeleteMode && selectedTagsForDeletion.includes(tag.name) && <Check className="h-4 w-4 text-red-600" />}
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full border"
                      style={{ backgroundColor: tag.color || '#6366f1' }}
                    />
                    <span className={cn(
                      "text-sm",
                      isDeleteMode && selectedTagsForDeletion.includes(tag.name) && "text-red-700"
                    )}>
                      {tag.name}
                    </span>
                  </div>
                </div>
              ))}
            </>
          )}
          {filteredTags.length === 0 && <div className="px-3 py-2 text-sm text-gray-500">No tags found</div>}
        </div>
        <div className="p-2 border-t flex justify-end gap-2">
          <Button variant="ghost" size="sm" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button size="sm" onClick={handleApply}>
            Apply
          </Button>
        </div>
      </div>,
    document.body
  ) : null
}
